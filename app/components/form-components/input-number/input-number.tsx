import { RDSStepper, RDSStepperProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledStepperProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSStepperProps, "name" | "value" | "onChange" | "onBlur">;

export function InputNumber<T extends FieldValues>({
  name,
  control,
  label,
  helperText,
  ...rest
}: ControlledStepperProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <RDSStepper
          {...field}
          value={field.value}
          helperText={fieldState.error?.message ?? helperText}
          isInvalid={!!fieldState.error}
          {...rest}
        />
      )}
    />
  );
}
